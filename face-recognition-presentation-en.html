<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Face Recognition & Liveness Detection Technology</title>
    
    <!-- RevealJS CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/reveal.js@4.3.1/dist/reveal.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/reveal.js@4.3.1/dist/theme/white.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #0066cc;
            --secondary-color: #6366f1;
            --accent-color: #06b6d4;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --text-dark: #1e293b;
            --text-light: #64748b;
            --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --tech-gradient: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
            --card-shadow: 0 10px 25px rgba(0,0,0,0.1);
            --border-radius: 12px;
        }

        .reveal {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            color: var(--text-dark);
            font-weight: 400;
        }

        .reveal .slides {
            text-align: left;
        }

        .reveal h1, .reveal h2, .reveal h3 {
            font-family: 'Inter', sans-serif;
            font-weight: 600;
            line-height: 1.2;
            margin-bottom: 1em;
            letter-spacing: -0.025em;
        }

        .reveal h1 {
            font-size: 2.75em;
            font-weight: 700;
            background: var(--tech-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-align: center;
            letter-spacing: -0.05em;
        }

        .reveal h2 {
            font-size: 2.2em;
            color: var(--primary-color);
            border-bottom: 3px solid var(--accent-color);
            padding-bottom: 0.3em;
            margin-bottom: 1.2em;
            font-weight: 600;
        }

        .reveal h3 {
            font-size: 1.5em;
            color: var(--secondary-color);
            margin-bottom: 0.8em;
            font-weight: 500;
        }

        .reveal h4 {
            font-size: 1.2em;
            color: var(--text-dark);
            margin-bottom: 0.6em;
            font-weight: 500;
        }

        .reveal p, .reveal li {
            font-size: 1.1em;
            line-height: 1.7;
            margin-bottom: 0.8em;
            color: var(--text-dark);
        }

        .hero-section {
            text-align: center;
            background: var(--tech-gradient);
            color: white;
            padding: 3.5em 2em;
            border-radius: 20px;
            margin: 2em 0;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .hero-section * {
            position: relative;
            z-index: 1;
        }

        .hero-section h1 {
            color: white;
            -webkit-text-fill-color: white;
            margin-bottom: 0.5em;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .hero-subtitle {
            font-size: 1.4em;
            opacity: 0.95;
            margin-bottom: 2em;
            font-weight: 300;
        }

        .tech-card {
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border-radius: var(--border-radius);
            padding: 2.5em;
            margin: 1.5em 0;
            box-shadow: var(--card-shadow);
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
            position: relative;
        }

        .tech-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: var(--tech-gradient);
            border-radius: 2px 0 0 2px;
        }

        .tech-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.8em;
            margin: 2.5em 0;
        }

        .feature-item {
            background: white;
            border-radius: var(--border-radius);
            padding: 2em;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
            text-align: center;
        }

        .feature-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.12);
            border-color: var(--accent-color);
        }

        .feature-icon {
            font-size: 3em;
            background: var(--tech-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.8em;
            display: block;
        }

        .process-flow {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin: 3em 0;
            flex-wrap: wrap;
            gap: 1.5em;
        }

        .process-step {
            flex: 1;
            min-width: 220px;
            text-align: center;
            position: relative;
        }

        .process-step::after {
            content: '→';
            position: absolute;
            right: -1.2em;
            top: 2em;
            font-size: 1.8em;
            color: var(--accent-color);
            font-weight: bold;
        }

        .process-step:last-child::after {
            display: none;
        }

        .step-number {
            background: var(--tech-gradient);
            color: white;
            width: 3.5em;
            height: 3.5em;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1em;
            font-weight: 600;
            font-size: 1.3em;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .stats-container {
            display: flex;
            justify-content: space-around;
            margin: 2.5em 0;
            flex-wrap: wrap;
            gap: 1.5em;
        }

        .stat-item {
            text-align: center;
            flex: 1;
            min-width: 140px;
            background: rgba(255,255,255,0.2);
            padding: 1.5em;
            border-radius: var(--border-radius);
            backdrop-filter: blur(10px);
        }

        .stat-number {
            font-size: 2.8em;
            font-weight: 700;
            color: white;
            display: block;
            margin-bottom: 0.2em;
        }

        .stat-label {
            color: rgba(255,255,255,0.9);
            font-size: 0.95em;
            font-weight: 400;
        }

        .highlight-box {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 1px solid #f59e0b;
            border-radius: var(--border-radius);
            padding: 1.8em;
            margin: 2em 0;
            position: relative;
        }

        .highlight-box::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: var(--warning-color);
            border-radius: 2px 0 0 2px;
        }

        .security-badges {
            display: flex;
            flex-wrap: wrap;
            gap: 0.8em;
            margin: 1.5em 0;
            justify-content: center;
        }

        .security-badge {
            display: inline-flex;
            align-items: center;
            background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
            color: white;
            padding: 0.6em 1.2em;
            border-radius: 25px;
            font-weight: 500;
            font-size: 0.9em;
            box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
            transition: transform 0.2s ease;
        }

        .security-badge:hover {
            transform: translateY(-2px);
        }

        .security-badge i {
            margin-right: 0.6em;
        }

        .tech-specs {
            background: #f8fafc;
            border-radius: var(--border-radius);
            padding: 2em;
            margin: 2em 0;
            border: 1px solid #e2e8f0;
        }

        .spec-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5em;
            margin-top: 1em;
        }

        .spec-item {
            text-align: center;
        }

        .spec-value {
            font-size: 1.8em;
            font-weight: 600;
            color: var(--primary-color);
            display: block;
        }

        .spec-label {
            color: var(--text-light);
            font-size: 0.9em;
            margin-top: 0.3em;
        }

        @media (max-height: 768px) {
            .reveal h1 { font-size: 2.2em; }
            .reveal h2 { font-size: 1.8em; }
            .reveal h3 { font-size: 1.3em; }
            .reveal p, .reveal li { font-size: 1em; }
            .tech-card { padding: 2em; }
            .feature-item { padding: 1.5em; }
            .hero-section { padding: 2.5em 1.5em; }
        }

        @media (max-width: 768px) {
            .process-flow { flex-direction: column; align-items: center; }
            .process-step::after { display: none; }
            .feature-grid { grid-template-columns: 1fr; }
            .stats-container { flex-direction: column; }
            .security-badges { justify-content: flex-start; }
        }
    </style>
</head>

<body>
    <div class="reveal">
        <div class="slides">
            <!-- Title Slide -->
            <section>
                <div class="hero-section">
                    <h1><i class="fas fa-user-check"></i> Face Recognition & Liveness Detection</h1>
                    <p class="hero-subtitle">Advanced AI-Powered Biometric Authentication Technology</p>
                    <div class="stats-container">
                        <div class="stat-item">
                            <span class="stat-number">99.9%</span>
                            <span class="stat-label">Accuracy Rate</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">&lt;100ms</span>
                            <span class="stat-label">Response Time</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">AI-Driven</span>
                            <span class="stat-label">Deep Learning</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">Multi-Modal</span>
                            <span class="stat-label">Detection</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Face Recognition Technology -->
            <section>
                <h2><i class="fas fa-brain"></i> Face Recognition Technology</h2>

                <div class="tech-card">
                    <h3><i class="fas fa-cogs"></i> Core Technology Principles</h3>
                    <p>Face recognition is a biometric identification technology based on facial feature information. It utilizes deep learning algorithms to extract facial feature vectors, enabling high-precision identity verification across various applications and environments.</p>
                </div>

                <div class="process-flow">
                    <div class="process-step">
                        <div class="step-number">1</div>
                        <h4>Face Detection</h4>
                        <p>Locate and isolate facial regions within images or video streams</p>
                    </div>
                    <div class="process-step">
                        <div class="step-number">2</div>
                        <h4>Feature Extraction</h4>
                        <p>Extract key facial landmarks and distinctive features</p>
                    </div>
                    <div class="process-step">
                        <div class="step-number">3</div>
                        <h4>Feature Matching</h4>
                        <p>Compare extracted features against database templates</p>
                    </div>
                    <div class="process-step">
                        <div class="step-number">4</div>
                        <h4>Identity Verification</h4>
                        <p>Output recognition results with confidence scores</p>
                    </div>
                </div>

                <div class="feature-grid">
                    <div class="feature-item">
                        <i class="fas fa-eye feature-icon"></i>
                        <h4>High Precision</h4>
                        <p>Deep convolutional neural networks achieve 99.9%+ accuracy, supporting multi-angle and various lighting conditions for precise identification.</p>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-bolt feature-icon"></i>
                        <h4>Real-time Processing</h4>
                        <p>Optimized algorithm architecture enables sub-100ms recognition time, supporting high-concurrency real-time authentication scenarios.</p>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-shield-alt feature-icon"></i>
                        <h4>Privacy Protection</h4>
                        <p>Feature vector storage without original face images, compliant with data protection regulations, ensuring user privacy security.</p>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-expand-arrows-alt feature-icon"></i>
                        <h4>Versatile Applications</h4>
                        <p>Supports access control, attendance, payments, security, and more. Flexible integration into various business systems.</p>
                    </div>
                </div>

                <div class="tech-specs">
                    <h3><i class="fas fa-chart-line"></i> Technical Specifications</h3>
                    <div class="spec-grid">
                        <div class="spec-item">
                            <span class="spec-value">1:N</span>
                            <div class="spec-label">Identification Mode</div>
                        </div>
                        <div class="spec-item">
                            <span class="spec-value">1:1</span>
                            <div class="spec-label">Verification Mode</div>
                        </div>
                        <div class="spec-item">
                            <span class="spec-value">10M+</span>
                            <div class="spec-label">Database Capacity</div>
                        </div>
                        <div class="spec-item">
                            <span class="spec-value">512D</span>
                            <div class="spec-label">Feature Vector</div>
                        </div>
                    </div>
                </div>

                <div class="highlight-box">
                    <strong><i class="fas fa-lightbulb"></i> Technology Advantage:</strong>
                    Deep learning-based face recognition technology features adaptive learning capabilities, continuously optimizing recognition performance to meet diverse environmental and user group requirements.
                </div>
            </section>

            <!-- Liveness Detection Technology -->
            <section>
                <h2><i class="fas fa-user-shield"></i> Liveness Detection Technology</h2>

                <div class="tech-card">
                    <h3><i class="fas fa-heartbeat"></i> Technology Overview</h3>
                    <p>Liveness detection technology identifies biological living characteristics of human faces, effectively preventing spoofing attacks from photos, videos, 3D models, and other deceptive methods, ensuring the authenticity and security of identity verification.</p>
                </div>

                <div class="feature-grid">
                    <div class="feature-item">
                        <i class="fas fa-camera feature-icon"></i>
                        <h4>RGB Liveness Detection</h4>
                        <p>Analyzes facial texture, lighting reflection, and micro-expressions through standard cameras to detect static attacks like photos and screen replays.</p>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-cube feature-icon"></i>
                        <h4>3D Structured Light</h4>
                        <p>Utilizes infrared structured light technology to capture facial depth information, accurately distinguishing real faces from flat images.</p>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-thermometer-half feature-icon"></i>
                        <h4>Thermal Imaging</h4>
                        <p>Detects human facial temperature distribution patterns, identifying genuine biological body heat to prevent mask and silicone attacks.</p>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-hand-paper feature-icon"></i>
                        <h4>Action-based Detection</h4>
                        <p>Requires users to perform random actions like blinking, mouth opening, or head nodding, confirming user cooperation through behavioral analysis.</p>
                    </div>
                </div>

                <div class="process-flow">
                    <div class="process-step">
                        <div class="step-number">1</div>
                        <h4>Multi-modal Capture</h4>
                        <p>Simultaneously acquire RGB, depth, infrared, and other multi-dimensional data</p>
                    </div>
                    <div class="process-step">
                        <div class="step-number">2</div>
                        <h4>Feature Fusion</h4>
                        <p>Comprehensively analyze multiple biological liveness characteristics</p>
                    </div>
                    <div class="process-step">
                        <div class="step-number">3</div>
                        <h4>Liveness Assessment</h4>
                        <p>AI-based algorithms determine liveness authenticity</p>
                    </div>
                    <div class="process-step">
                        <div class="step-number">4</div>
                        <h4>Security Validation</h4>
                        <p>Output liveness detection results and risk assessment</p>
                    </div>
                </div>

                <div style="margin: 2.5em 0;">
                    <h3><i class="fas fa-shield-virus"></i> Anti-Spoofing Capabilities</h3>
                    <div class="security-badges">
                        <span class="security-badge"><i class="fas fa-ban"></i>Photo Attack Prevention</span>
                        <span class="security-badge"><i class="fas fa-ban"></i>Video Replay Prevention</span>
                        <span class="security-badge"><i class="fas fa-ban"></i>Mask Attack Prevention</span>
                        <span class="security-badge"><i class="fas fa-ban"></i>3D Model Prevention</span>
                        <span class="security-badge"><i class="fas fa-ban"></i>Screen Replay Prevention</span>
                        <span class="security-badge"><i class="fas fa-ban"></i>Deepfake Prevention</span>
                    </div>
                </div>

                <div class="tech-specs">
                    <h3><i class="fas fa-chart-bar"></i> Performance Metrics</h3>
                    <div class="spec-grid">
                        <div class="spec-item">
                            <span class="spec-value">99.8%</span>
                            <div class="spec-label">Attack Rejection Rate</div>
                        </div>
                        <div class="spec-item">
                            <span class="spec-value">98.5%</span>
                            <div class="spec-label">True Acceptance Rate</div>
                        </div>
                        <div class="spec-item">
                            <span class="spec-value">&lt;200ms</span>
                            <div class="spec-label">Detection Time</div>
                        </div>
                        <div class="spec-item">
                            <span class="spec-value">Multi-Modal</span>
                            <div class="spec-label">Detection Methods</div>
                        </div>
                    </div>
                </div>

                <div class="highlight-box">
                    <strong><i class="fas fa-exclamation-triangle"></i> Security Assurance:</strong>
                    Multi-layered liveness detection technologies combined achieve 99.8%+ attack rejection rate, providing reliable security for financial payments, identity authentication, and other high-security scenarios.
                </div>
            </section>
        </div>
    </div>

    <!-- RevealJS JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/reveal.js@4.3.1/dist/reveal.js"></script>

    <script>
        Reveal.initialize({
            hash: true,
            transition: 'slide',
            transitionSpeed: 'default',
            backgroundTransition: 'fade',
            controls: true,
            progress: true,
            center: true,
            touch: true,
            loop: false,
            rtl: false,
            navigationMode: 'default',
            shuffle: false,
            fragments: true,
            fragmentInURL: false,
            embedded: false,
            help: true,
            showNotes: false,
            autoPlayMedia: null,
            preloadIframes: null,
            autoSlide: 0,
            autoSlideStoppable: true,
            autoSlideMethod: null,
            defaultTiming: null,
            mouseWheel: false,
            hideInactiveCursor: true,
            hideCursorTime: 5000,
            previewLinks: false,
            width: 960,
            height: 700,
            margin: 0.04,
            minScale: 0.2,
            maxScale: 2.0,
            disableLayout: false,
            keyboard: {
                13: 'next',
                27: function() {},
                32: null
            }
        });

        // Enhanced micro-interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Card hover effects with enhanced animations
            const cards = document.querySelectorAll('.tech-card, .feature-item');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                    this.style.boxShadow = '0 20px 40px rgba(0,0,0,0.15)';
                });
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                    this.style.boxShadow = '';
                });
            });

            // Staggered animation for process steps
            const steps = document.querySelectorAll('.process-step');
            steps.forEach((step, index) => {
                step.style.animationDelay = `${index * 0.15}s`;
                step.style.animation = 'slideInUp 0.8s ease forwards';
            });

            // Security badges animation
            const badges = document.querySelectorAll('.security-badge');
            badges.forEach((badge, index) => {
                badge.style.animationDelay = `${index * 0.1}s`;
                badge.style.animation = 'fadeInScale 0.6s ease forwards';
            });

            // Stats counter animation
            const statNumbers = document.querySelectorAll('.stat-number, .spec-value');
            const observerOptions = {
                threshold: 0.5,
                rootMargin: '0px 0px -100px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.animation = 'countUp 1s ease forwards';
                    }
                });
            }, observerOptions);

            statNumbers.forEach(stat => observer.observe(stat));
        });

        // Enhanced CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInUp {
                from {
                    opacity: 0;
                    transform: translateY(40px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            @keyframes fadeInScale {
                from {
                    opacity: 0;
                    transform: scale(0.8);
                }
                to {
                    opacity: 1;
                    transform: scale(1);
                }
            }

            @keyframes countUp {
                from {
                    opacity: 0;
                    transform: scale(0.5);
                }
                to {
                    opacity: 1;
                    transform: scale(1);
                }
            }

            .process-step, .security-badge {
                opacity: 0;
            }

            .feature-item {
                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            }

            .security-badge {
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
