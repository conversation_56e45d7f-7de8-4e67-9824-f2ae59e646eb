<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>人脸识别与活体校验技术</title>
    
    <!-- RevealJS CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/reveal.js@4.3.1/dist/reveal.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/reveal.js@4.3.1/dist/theme/white.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #7c3aed;
            --accent-color: #06b6d4;
            --text-dark: #1e293b;
            --text-light: #64748b;
            --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --card-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .reveal {
            font-family: 'Noto Sans SC', Tahoma, Arial, Roboto, "Droid Sans", "Helvetica Neue", "Droid Sans Fallback", "Heiti SC", "Hiragino Sans GB", Simsun, sans-serif;
            color: var(--text-dark);
        }

        .reveal .slides {
            text-align: left;
        }

        .reveal h1, .reveal h2, .reveal h3 {
            font-family: 'Noto Serif SC', serif;
            font-weight: 600;
            line-height: 1.2;
            margin-bottom: 1em;
        }

        .reveal h1 {
            font-size: 2.5em;
            background: var(--bg-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-align: center;
        }

        .reveal h2 {
            font-size: 2em;
            color: var(--primary-color);
            border-bottom: 3px solid var(--accent-color);
            padding-bottom: 0.3em;
            margin-bottom: 1em;
        }

        .reveal h3 {
            font-size: 1.4em;
            color: var(--secondary-color);
            margin-bottom: 0.8em;
        }

        .reveal p, .reveal li {
            font-size: 1.1em;
            line-height: 1.6;
            margin-bottom: 0.8em;
        }

        .tech-card {
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 15px;
            padding: 2em;
            margin: 1em 0;
            box-shadow: var(--card-shadow);
            border-left: 5px solid var(--primary-color);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .tech-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5em;
            margin: 2em 0;
        }

        .feature-item {
            background: white;
            border-radius: 12px;
            padding: 1.5em;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
        }

        .feature-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.12);
            border-color: var(--accent-color);
        }

        .feature-icon {
            font-size: 2.5em;
            color: var(--primary-color);
            margin-bottom: 0.5em;
            display: block;
        }

        .process-flow {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 2em 0;
            flex-wrap: wrap;
            gap: 1em;
        }

        .process-step {
            flex: 1;
            min-width: 200px;
            text-align: center;
            position: relative;
        }

        .process-step::after {
            content: '→';
            position: absolute;
            right: -1em;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.5em;
            color: var(--accent-color);
        }

        .process-step:last-child::after {
            display: none;
        }

        .step-number {
            background: var(--bg-gradient);
            color: white;
            width: 3em;
            height: 3em;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1em;
            font-weight: bold;
            font-size: 1.2em;
        }

        .highlight-box {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border-left: 4px solid #f59e0b;
            padding: 1.5em;
            border-radius: 8px;
            margin: 1.5em 0;
        }

        .security-badge {
            display: inline-flex;
            align-items: center;
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 0.5em 1em;
            border-radius: 25px;
            font-weight: 500;
            margin: 0.5em 0.5em 0.5em 0;
        }

        .security-badge i {
            margin-right: 0.5em;
        }

        .title-slide {
            text-align: center;
            background: var(--bg-gradient);
            color: white;
            padding: 3em;
            border-radius: 20px;
            margin: 2em 0;
        }

        .title-slide h1 {
            color: white;
            -webkit-text-fill-color: white;
            margin-bottom: 0.5em;
        }

        .subtitle {
            font-size: 1.3em;
            opacity: 0.9;
            margin-bottom: 2em;
        }

        .tech-stats {
            display: flex;
            justify-content: space-around;
            margin: 2em 0;
            flex-wrap: wrap;
            gap: 1em;
        }

        .stat-item {
            text-align: center;
            flex: 1;
            min-width: 120px;
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: var(--primary-color);
            display: block;
        }

        .stat-label {
            color: var(--text-light);
            font-size: 0.9em;
        }

        @media (max-height: 768px) {
            .reveal h1 { font-size: 2em; }
            .reveal h2 { font-size: 1.6em; }
            .reveal h3 { font-size: 1.2em; }
            .reveal p, .reveal li { font-size: 1em; }
            .tech-card { padding: 1.5em; }
            .feature-item { padding: 1em; }
        }

        @media (max-width: 768px) {
            .process-flow { flex-direction: column; }
            .process-step::after { display: none; }
            .feature-grid { grid-template-columns: 1fr; }
            .tech-stats { flex-direction: column; }
        }
    </style>
</head>

<body>
    <div class="reveal">
        <div class="slides">
            <!-- 封面页 -->
            <section>
                <div class="title-slide">
                    <h1><i class="fas fa-user-check"></i> 人脸识别与活体校验技术</h1>
                    <p class="subtitle">Face Recognition & Liveness Detection Technology</p>
                    <div class="tech-stats">
                        <div class="stat-item">
                            <span class="stat-number">99.9%</span>
                            <span class="stat-label">识别准确率</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">&lt;100ms</span>
                            <span class="stat-label">响应时间</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">AI驱动</span>
                            <span class="stat-label">深度学习</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 人脸识别技术页面 -->
            <section>
                <h2><i class="fas fa-brain"></i> 人脸识别技术</h2>

                <div class="tech-card">
                    <h3><i class="fas fa-cogs"></i> 核心技术原理</h3>
                    <p>人脸识别是一种基于人的脸部特征信息进行身份识别的生物识别技术，通过深度学习算法提取面部特征向量，实现高精度的身份验证。</p>
                </div>

                <div class="process-flow">
                    <div class="process-step">
                        <div class="step-number">1</div>
                        <h4>人脸检测</h4>
                        <p>定位图像中的人脸区域</p>
                    </div>
                    <div class="process-step">
                        <div class="step-number">2</div>
                        <h4>特征提取</h4>
                        <p>提取关键面部特征点</p>
                    </div>
                    <div class="process-step">
                        <div class="step-number">3</div>
                        <h4>特征比对</h4>
                        <p>与数据库进行匹配验证</p>
                    </div>
                    <div class="process-step">
                        <div class="step-number">4</div>
                        <h4>身份确认</h4>
                        <p>输出识别结果和置信度</p>
                    </div>
                </div>

                <div class="feature-grid">
                    <div class="feature-item">
                        <i class="fas fa-eye feature-icon"></i>
                        <h4>高精度识别</h4>
                        <p>采用深度卷积神经网络，识别准确率达99.9%以上，支持多角度、多光照条件下的精准识别。</p>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-bolt feature-icon"></i>
                        <h4>实时处理</h4>
                        <p>优化算法架构，单次识别耗时小于100毫秒，支持高并发场景下的实时身份验证需求。</p>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-shield-alt feature-icon"></i>
                        <h4>隐私保护</h4>
                        <p>采用特征向量存储，不保存原始人脸图像，符合数据保护法规，确保用户隐私安全。</p>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-expand-arrows-alt feature-icon"></i>
                        <h4>广泛适用</h4>
                        <p>支持门禁、考勤、支付、安防等多种应用场景，可灵活集成到各类业务系统中。</p>
                    </div>
                </div>

                <div class="highlight-box">
                    <strong><i class="fas fa-lightbulb"></i> 技术优势：</strong>
                    基于深度学习的人脸识别技术具有自适应学习能力，能够持续优化识别效果，适应不同环境和用户群体的需求。
                </div>
            </section>

            <!-- 人脸活体校验技术页面 -->
            <section>
                <h2><i class="fas fa-user-shield"></i> 人脸活体校验技术</h2>

                <div class="tech-card">
                    <h3><i class="fas fa-heartbeat"></i> 技术概述</h3>
                    <p>人脸活体校验技术通过检测人脸的生物活体特征，有效防范照片、视频、3D模型等欺骗攻击，确保身份验证的真实性和安全性。</p>
                </div>

                <div class="feature-grid">
                    <div class="feature-item">
                        <i class="fas fa-camera feature-icon"></i>
                        <h4>RGB活体检测</h4>
                        <p>通过普通摄像头分析面部纹理、光照反射等特征，检测静态攻击如照片、屏幕翻拍等欺骗手段。</p>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-cube feature-icon"></i>
                        <h4>3D结构光检测</h4>
                        <p>利用红外结构光技术获取面部深度信息，精确区分真实人脸与平面图像，防范高级攻击手段。</p>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-thermometer-half feature-icon"></i>
                        <h4>红外热成像</h4>
                        <p>检测人体面部温度分布特征，识别真实的生物体温，有效防范面具、硅胶等物理攻击。</p>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-hand-paper feature-icon"></i>
                        <h4>动作配合检测</h4>
                        <p>要求用户完成眨眼、张嘴、点头等随机动作，通过行为分析确认用户的主观配合意愿。</p>
                    </div>
                </div>

                <div class="process-flow">
                    <div class="process-step">
                        <div class="step-number">1</div>
                        <h4>多模态采集</h4>
                        <p>同时获取RGB、深度、红外等多维度数据</p>
                    </div>
                    <div class="process-step">
                        <div class="step-number">2</div>
                        <h4>特征融合</h4>
                        <p>综合分析多种生物活体特征</p>
                    </div>
                    <div class="process-step">
                        <div class="step-number">3</div>
                        <h4>活体判断</h4>
                        <p>基于AI算法进行活体真伪判定</p>
                    </div>
                    <div class="process-step">
                        <div class="step-number">4</div>
                        <h4>安全验证</h4>
                        <p>输出活体检测结果和风险评估</p>
                    </div>
                </div>

                <div style="margin: 2em 0;">
                    <h3><i class="fas fa-shield-virus"></i> 防攻击能力</h3>
                    <div style="display: flex; flex-wrap: wrap; gap: 0.5em; margin: 1em 0;">
                        <span class="security-badge"><i class="fas fa-ban"></i>防照片攻击</span>
                        <span class="security-badge"><i class="fas fa-ban"></i>防视频攻击</span>
                        <span class="security-badge"><i class="fas fa-ban"></i>防面具攻击</span>
                        <span class="security-badge"><i class="fas fa-ban"></i>防3D模型攻击</span>
                        <span class="security-badge"><i class="fas fa-ban"></i>防屏幕翻拍</span>
                    </div>
                </div>

                <div class="highlight-box">
                    <strong><i class="fas fa-exclamation-triangle"></i> 安全保障：</strong>
                    多重活体检测技术相结合，攻击拒绝率达99.8%以上，为金融支付、身份认证等高安全场景提供可靠保障。
                </div>
            </section>
        </div>
    </div>

    <!-- RevealJS JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/reveal.js@4.3.1/dist/reveal.js"></script>

    <script>
        Reveal.initialize({
            hash: true,
            transition: 'slide',
            transitionSpeed: 'default',
            backgroundTransition: 'fade',
            controls: true,
            progress: true,
            center: true,
            touch: true,
            loop: false,
            rtl: false,
            navigationMode: 'default',
            shuffle: false,
            fragments: true,
            fragmentInURL: false,
            embedded: false,
            help: true,
            showNotes: false,
            autoPlayMedia: null,
            preloadIframes: null,
            autoSlide: 0,
            autoSlideStoppable: true,
            autoSlideMethod: null,
            defaultTiming: null,
            mouseWheel: false,
            hideInactiveCursor: true,
            hideCursorTime: 5000,
            previewLinks: false,
            width: 960,
            height: 700,
            margin: 0.04,
            minScale: 0.2,
            maxScale: 2.0,
            disableLayout: false,
            keyboard: {
                13: 'next',
                27: function() {},
                32: null
            }
        });

        // 添加微交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为卡片添加悬停效果
            const cards = document.querySelectorAll('.tech-card, .feature-item');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px) scale(1.02)';
                });
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // 为步骤添加动画效果
            const steps = document.querySelectorAll('.process-step');
            steps.forEach((step, index) => {
                step.style.animationDelay = `${index * 0.2}s`;
                step.style.animation = 'fadeInUp 0.6s ease forwards';
            });
        });

        // CSS动画定义
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            .process-step {
                opacity: 0;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
