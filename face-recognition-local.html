<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Face Recognition & Liveness Detection Technology</title>
    
    <style>
        /* 基础样式重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #0066cc;
            --secondary-color: #6366f1;
            --accent-color: #06b6d4;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --text-dark: #1e293b;
            --text-light: #64748b;
            --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --tech-gradient: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
            --card-shadow: 0 10px 25px rgba(0,0,0,0.1);
            --border-radius: 12px;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', <PERSON><PERSON>, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            color: var(--text-dark);
            background: #f8fafc;
            overflow: hidden;
        }

        .presentation-container {
            width: 100vw;
            height: 100vh;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .slide {
            width: 90vw;
            height: 90vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            padding: 3rem;
            display: none;
            flex-direction: column;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .slide.active {
            display: flex;
        }

        .slide h1 {
            font-size: 3.5rem;
            font-weight: 700;
            background: var(--tech-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-align: center;
            margin-bottom: 1rem;
            letter-spacing: -0.05em;
        }

        .slide h2 {
            font-size: 2.5rem;
            color: var(--primary-color);
            border-bottom: 3px solid var(--accent-color);
            padding-bottom: 0.5rem;
            margin-bottom: 1.5rem;
            font-weight: 600;
        }

        .slide h3 {
            font-size: 1.8rem;
            color: var(--secondary-color);
            margin-bottom: 1rem;
            font-weight: 500;
        }

        .slide h4 {
            font-size: 1.4rem;
            color: var(--text-dark);
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .slide p {
            font-size: 1.2rem;
            line-height: 1.6;
            margin-bottom: 0.8rem;
            color: var(--text-dark);
        }

        /* Hero Section */
        .hero-section {
            text-align: center;
            background: var(--tech-gradient);
            color: white;
            padding: 4rem 3rem;
            border-radius: 20px;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
        }

        .hero-section h1 {
            color: white;
            -webkit-text-fill-color: white;
            margin-bottom: 1rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .hero-subtitle {
            font-size: 1.8rem;
            opacity: 0.95;
            margin-bottom: 3rem;
            font-weight: 300;
        }

        .stats-container {
            display: flex;
            justify-content: space-around;
            gap: 2rem;
            width: 100%;
        }

        .stat-item {
            text-align: center;
            flex: 1;
            background: rgba(255,255,255,0.2);
            padding: 2rem 1rem;
            border-radius: var(--border-radius);
            backdrop-filter: blur(10px);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: white;
            display: block;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: rgba(255,255,255,0.9);
            font-size: 1rem;
            font-weight: 400;
        }

        /* Tech Card */
        .tech-card {
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border-radius: var(--border-radius);
            padding: 2rem;
            margin: 1rem 0;
            box-shadow: var(--card-shadow);
            border: 1px solid #e2e8f0;
            position: relative;
            flex-shrink: 0;
        }

        .tech-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: var(--tech-gradient);
            border-radius: 2px 0 0 2px;
        }

        /* Process Flow */
        .process-flow {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin: 2rem 0;
            gap: 1rem;
            flex: 1;
        }

        .process-step {
            flex: 1;
            text-align: center;
            position: relative;
        }

        .process-step::after {
            content: '→';
            position: absolute;
            right: -0.8rem;
            top: 2rem;
            font-size: 1.5rem;
            color: var(--accent-color);
            font-weight: bold;
        }

        .process-step:last-child::after {
            display: none;
        }

        .step-number {
            background: var(--tech-gradient);
            color: white;
            width: 3rem;
            height: 3rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-weight: 600;
            font-size: 1.2rem;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        /* Feature Grid */
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1.5rem;
            margin: 1.5rem 0;
            flex: 1;
        }

        .feature-item {
            background: white;
            border-radius: var(--border-radius);
            padding: 1.5rem;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            border: 1px solid #e2e8f0;
            text-align: center;
            display: flex;
            flex-direction: column;
            justify-content: center;
            transition: transform 0.3s ease;
        }

        .feature-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.12);
        }

        .feature-icon {
            font-size: 2.5rem;
            background: var(--tech-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
            display: block;
        }

        /* Navigation */
        .nav-controls {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            display: flex;
            gap: 1rem;
            z-index: 1000;
        }

        .nav-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 1rem 1.5rem;
            border-radius: 50px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0,102,204,0.3);
        }

        .nav-btn:hover {
            background: var(--secondary-color);
            transform: translateY(-2px);
        }

        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* Progress Bar */
        .progress-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            height: 4px;
            background: var(--tech-gradient);
            transition: width 0.3s ease;
            z-index: 1000;
        }

        /* Slide Indicator */
        .slide-indicator {
            position: fixed;
            bottom: 2rem;
            left: 2rem;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            z-index: 1000;
        }

        /* 图标字体替代 */
        .icon {
            display: inline-block;
            width: 1em;
            height: 1em;
            margin-right: 0.5em;
            vertical-align: middle;
        }

        .icon-brain::before { content: '🧠'; }
        .icon-cogs::before { content: '⚙️'; }
        .icon-eye::before { content: '👁️'; }
        .icon-bolt::before { content: '⚡'; }
        .icon-shield::before { content: '🛡️'; }
        .icon-expand::before { content: '🔄'; }
        .icon-user-shield::before { content: '🔐'; }
        .icon-heartbeat::before { content: '💓'; }
        .icon-camera::before { content: '📷'; }
        .icon-cube::before { content: '📦'; }
        .icon-thermometer::before { content: '🌡️'; }
        .icon-hand::before { content: '✋'; }
        .icon-chart::before { content: '📊'; }
        .icon-lightbulb::before { content: '💡'; }
        .icon-warning::before { content: '⚠️'; }
        .icon-ban::before { content: '🚫'; }
        .icon-check::before { content: '✅'; }

        @media (max-width: 1200px) {
            .feature-grid { grid-template-columns: repeat(2, 1fr); }
        }

        @media (max-height: 800px) {
            .slide h1 { font-size: 3rem; }
            .slide h2 { font-size: 2rem; }
            .slide h3 { font-size: 1.5rem; }
            .slide p { font-size: 1rem; }
            .tech-card { padding: 1.5rem; }
            .feature-item { padding: 1rem; }
            .hero-section { padding: 3rem 2rem; }
            .hero-subtitle { font-size: 1.5rem; margin-bottom: 2rem; }
        }
    </style>
</head>

<body>
    <div class="presentation-container">
        <!-- Slide 1: Title -->
        <div class="slide active">
            <div class="hero-section">
                <h1><span class="icon icon-check"></span> Face Recognition & Liveness Detection</h1>
                <p class="hero-subtitle">Advanced AI-Powered Biometric Authentication Technology</p>
                <div class="stats-container">
                    <div class="stat-item">
                        <span class="stat-number">99.9%</span>
                        <span class="stat-label">Accuracy Rate</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">&lt;100ms</span>
                        <span class="stat-label">Response Time</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">AI-Driven</span>
                        <span class="stat-label">Deep Learning</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">Multi-Modal</span>
                        <span class="stat-label">Detection</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 2: Face Recognition Technology -->
        <div class="slide">
            <h2><span class="icon icon-brain"></span> Face Recognition Technology</h2>

            <div class="tech-card">
                <h3><span class="icon icon-cogs"></span> Core Technology Principles</h3>
                <p>Face recognition is a biometric identification technology based on facial feature information. It utilizes deep learning algorithms to extract facial feature vectors, enabling high-precision identity verification across various applications and environments.</p>
            </div>

            <div class="process-flow">
                <div class="process-step">
                    <div class="step-number">1</div>
                    <h4>Face Detection</h4>
                    <p>Locate and isolate facial regions within images or video streams</p>
                </div>
                <div class="process-step">
                    <div class="step-number">2</div>
                    <h4>Feature Extraction</h4>
                    <p>Extract key facial landmarks and distinctive features</p>
                </div>
                <div class="process-step">
                    <div class="step-number">3</div>
                    <h4>Feature Matching</h4>
                    <p>Compare extracted features against database templates</p>
                </div>
                <div class="process-step">
                    <div class="step-number">4</div>
                    <h4>Identity Verification</h4>
                    <p>Output recognition results with confidence scores</p>
                </div>
            </div>

            <div class="feature-grid">
                <div class="feature-item">
                    <span class="icon icon-eye feature-icon"></span>
                    <h4>High Precision</h4>
                    <p>Deep CNNs achieve 99.9%+ accuracy with multi-angle support</p>
                </div>
                <div class="feature-item">
                    <span class="icon icon-bolt feature-icon"></span>
                    <h4>Real-time Processing</h4>
                    <p>Sub-100ms recognition time for high-concurrency scenarios</p>
                </div>
                <div class="feature-item">
                    <span class="icon icon-shield feature-icon"></span>
                    <h4>Privacy Protection</h4>
                    <p>Feature vector storage compliant with data protection</p>
                </div>
                <div class="feature-item">
                    <span class="icon icon-expand feature-icon"></span>
                    <h4>Versatile Applications</h4>
                    <p>Flexible integration into various business systems</p>
                </div>
            </div>

            <div style="display: flex; gap: 1.5rem; margin-top: 1rem;">
                <div style="flex: 1; background: #f8fafc; padding: 1.5rem; border-radius: 12px; border: 1px solid #e2e8f0;">
                    <h3><span class="icon icon-chart"></span> Technical Specifications</h3>
                    <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 1rem; margin-top: 1rem;">
                        <div style="text-align: center;">
                            <div style="font-size: 1.6rem; font-weight: 600; color: var(--primary-color);">1:N</div>
                            <div style="color: var(--text-light); font-size: 0.9rem;">Identification Mode</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 1.6rem; font-weight: 600; color: var(--primary-color);">1:1</div>
                            <div style="color: var(--text-light); font-size: 0.9rem;">Verification Mode</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 1.6rem; font-weight: 600; color: var(--primary-color);">10M+</div>
                            <div style="color: var(--text-light); font-size: 0.9rem;">Database Capacity</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 1.6rem; font-weight: 600; color: var(--primary-color);">512D</div>
                            <div style="color: var(--text-light); font-size: 0.9rem;">Feature Vector</div>
                        </div>
                    </div>
                </div>
                <div style="flex: 1; background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%); border: 1px solid #f59e0b; border-radius: 12px; padding: 1.5rem; position: relative;">
                    <div style="position: absolute; left: 0; top: 0; bottom: 0; width: 4px; background: var(--warning-color); border-radius: 2px 0 0 2px;"></div>
                    <strong><span class="icon icon-lightbulb"></span> Technology Advantage:</strong>
                    Deep learning-based face recognition technology features adaptive learning capabilities, continuously optimizing recognition performance to meet diverse environmental and user group requirements.
                </div>
            </div>
        </div>

        <!-- Slide 3: Liveness Detection Technology -->
        <div class="slide">
            <h2><span class="icon icon-user-shield"></span> Liveness Detection Technology</h2>

            <div class="tech-card">
                <h3><span class="icon icon-heartbeat"></span> Technology Overview</h3>
                <p>Liveness detection technology identifies biological living characteristics of human faces, effectively preventing spoofing attacks from photos, videos, 3D models, and other deceptive methods, ensuring the authenticity and security of identity verification.</p>
            </div>

            <div class="feature-grid">
                <div class="feature-item">
                    <span class="icon icon-camera feature-icon"></span>
                    <h4>RGB Liveness Detection</h4>
                    <p>Analyzes facial texture and micro-expressions to detect static attacks</p>
                </div>
                <div class="feature-item">
                    <span class="icon icon-cube feature-icon"></span>
                    <h4>3D Structured Light</h4>
                    <p>Captures facial depth information to distinguish real faces from flat images</p>
                </div>
                <div class="feature-item">
                    <span class="icon icon-thermometer feature-icon"></span>
                    <h4>Thermal Imaging</h4>
                    <p>Detects body heat patterns to prevent mask and silicone attacks</p>
                </div>
                <div class="feature-item">
                    <span class="icon icon-hand feature-icon"></span>
                    <h4>Action-based Detection</h4>
                    <p>Requires random user actions to confirm cooperation and liveness</p>
                </div>
            </div>

            <div class="process-flow">
                <div class="process-step">
                    <div class="step-number">1</div>
                    <h4>Multi-modal Capture</h4>
                    <p>Acquire RGB, depth, infrared data simultaneously</p>
                </div>
                <div class="process-step">
                    <div class="step-number">2</div>
                    <h4>Feature Fusion</h4>
                    <p>Analyze multiple biological liveness characteristics</p>
                </div>
                <div class="process-step">
                    <div class="step-number">3</div>
                    <h4>Liveness Assessment</h4>
                    <p>AI algorithms determine liveness authenticity</p>
                </div>
                <div class="process-step">
                    <div class="step-number">4</div>
                    <h4>Security Validation</h4>
                    <p>Output detection results and risk assessment</p>
                </div>
            </div>

            <div style="display: flex; gap: 1.5rem; align-items: flex-start; margin-top: 1rem;">
                <div style="flex: 1;">
                    <h3><span class="icon icon-ban"></span> Anti-Spoofing Capabilities</h3>
                    <div style="display: flex; flex-wrap: wrap; gap: 0.8rem; margin: 1rem 0;">
                        <span style="display: inline-flex; align-items: center; background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%); color: white; padding: 0.5rem 1rem; border-radius: 20px; font-weight: 500; font-size: 0.9rem;"><span class="icon icon-ban"></span>Photo Attack</span>
                        <span style="display: inline-flex; align-items: center; background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%); color: white; padding: 0.5rem 1rem; border-radius: 20px; font-weight: 500; font-size: 0.9rem;"><span class="icon icon-ban"></span>Video Replay</span>
                        <span style="display: inline-flex; align-items: center; background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%); color: white; padding: 0.5rem 1rem; border-radius: 20px; font-weight: 500; font-size: 0.9rem;"><span class="icon icon-ban"></span>Mask Attack</span>
                        <span style="display: inline-flex; align-items: center; background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%); color: white; padding: 0.5rem 1rem; border-radius: 20px; font-weight: 500; font-size: 0.9rem;"><span class="icon icon-ban"></span>3D Model</span>
                        <span style="display: inline-flex; align-items: center; background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%); color: white; padding: 0.5rem 1rem; border-radius: 20px; font-weight: 500; font-size: 0.9rem;"><span class="icon icon-ban"></span>Screen Replay</span>
                        <span style="display: inline-flex; align-items: center; background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%); color: white; padding: 0.5rem 1rem; border-radius: 20px; font-weight: 500; font-size: 0.9rem;"><span class="icon icon-ban"></span>Deepfake</span>
                    </div>
                </div>
                <div style="flex: 1; background: #f8fafc; padding: 1.5rem; border-radius: 12px; border: 1px solid #e2e8f0;">
                    <h3><span class="icon icon-chart"></span> Performance Metrics</h3>
                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 1rem; margin-top: 1rem;">
                        <div style="text-align: center;">
                            <div style="font-size: 1.6rem; font-weight: 600; color: var(--primary-color);">99.8%</div>
                            <div style="color: var(--text-light); font-size: 0.9rem;">Attack Rejection Rate</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 1.6rem; font-weight: 600; color: var(--primary-color);">98.5%</div>
                            <div style="color: var(--text-light); font-size: 0.9rem;">True Acceptance Rate</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 1.6rem; font-weight: 600; color: var(--primary-color);">&lt;200ms</div>
                            <div style="color: var(--text-light); font-size: 0.9rem;">Detection Time</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 1.6rem; font-weight: 600; color: var(--primary-color);">Multi-Modal</div>
                            <div style="color: var(--text-light); font-size: 0.9rem;">Detection Methods</div>
                        </div>
                    </div>
                </div>
            </div>

            <div style="background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%); border: 1px solid #f59e0b; border-radius: 12px; padding: 1.5rem; margin-top: 1.5rem; position: relative;">
                <div style="position: absolute; left: 0; top: 0; bottom: 0; width: 4px; background: var(--warning-color); border-radius: 2px 0 0 2px;"></div>
                <strong><span class="icon icon-warning"></span> Security Assurance:</strong>
                Multi-layered liveness detection technologies combined achieve 99.8%+ attack rejection rate, providing reliable security for financial payments, identity authentication, and other high-security scenarios.
            </div>
        </div>
    </div>

    <!-- Navigation Controls -->
    <div class="nav-controls">
        <button class="nav-btn" id="prevBtn" onclick="changeSlide(-1)">← Previous</button>
        <button class="nav-btn" id="nextBtn" onclick="changeSlide(1)">Next →</button>
    </div>

    <!-- Progress Bar -->
    <div class="progress-bar" id="progressBar"></div>

    <!-- Slide Indicator -->
    <div class="slide-indicator" id="slideIndicator">1 / 3</div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;

        function showSlide(n) {
            slides[currentSlide].classList.remove('active');
            currentSlide = (n + totalSlides) % totalSlides;
            slides[currentSlide].classList.add('active');

            // Update progress bar
            const progress = ((currentSlide + 1) / totalSlides) * 100;
            document.getElementById('progressBar').style.width = progress + '%';

            // Update slide indicator
            document.getElementById('slideIndicator').textContent = `${currentSlide + 1} / ${totalSlides}`;

            // Update navigation buttons
            document.getElementById('prevBtn').disabled = currentSlide === 0;
            document.getElementById('nextBtn').disabled = currentSlide === totalSlides - 1;
        }

        function changeSlide(direction) {
            showSlide(currentSlide + direction);
        }

        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft' || e.key === 'ArrowUp') {
                changeSlide(-1);
            } else if (e.key === 'ArrowRight' || e.key === 'ArrowDown' || e.key === ' ') {
                changeSlide(1);
            }
        });

        // Initialize
        showSlide(0);

        // Add smooth transitions
        slides.forEach(slide => {
            slide.style.transition = 'opacity 0.5s ease-in-out';
        });
    </script>
</body>
</html>
