<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>人脸识别技术 - 从便利到安全</title>
    
    <style>
        /* 基础样式重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #2563eb;
            --secondary-color: #7c3aed;
            --accent-color: #06b6d4;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --text-dark: #1e293b;
            --text-light: #64748b;
            --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --tech-gradient: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
            --card-shadow: 0 10px 25px rgba(0,0,0,0.1);
            --border-radius: 12px;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            color: var(--text-dark);
            background: #f8fafc;
            overflow: hidden;
        }

        .presentation-container {
            width: 100vw;
            height: 100vh;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .slide {
            width: 90vw;
            height: 90vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            padding: 3rem;
            display: none;
            flex-direction: column;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .slide.active {
            display: flex;
        }

        .slide h1 {
            font-size: 3.5rem;
            font-weight: 700;
            background: var(--tech-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-align: center;
            margin-bottom: 1rem;
            letter-spacing: -0.05em;
        }

        .slide h2 {
            font-size: 2.5rem;
            color: var(--primary-color);
            border-bottom: 3px solid var(--accent-color);
            padding-bottom: 0.5rem;
            margin-bottom: 1.5rem;
            font-weight: 600;
        }

        .slide h3 {
            font-size: 1.8rem;
            color: var(--secondary-color);
            margin-bottom: 1rem;
            font-weight: 500;
        }

        .slide h4 {
            font-size: 1.4rem;
            color: var(--text-dark);
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .slide p {
            font-size: 1.3rem;
            line-height: 1.7;
            margin-bottom: 1rem;
            color: var(--text-dark);
        }

        /* Hero Section */
        .hero-section {
            text-align: center;
            background: var(--tech-gradient);
            color: white;
            padding: 4rem 3rem;
            border-radius: 20px;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
        }

        .hero-section h1 {
            color: white;
            -webkit-text-fill-color: white;
            margin-bottom: 1rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .hero-subtitle {
            font-size: 2rem;
            opacity: 0.95;
            margin-bottom: 3rem;
            font-weight: 300;
        }

        /* 应用场景展示 */
        .scenario-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 2rem;
            margin: 2rem 0;
        }

        .scenario-item {
            background: white;
            border-radius: var(--border-radius);
            padding: 2rem;
            box-shadow: var(--card-shadow);
            text-align: center;
            transition: transform 0.3s ease;
            border: 2px solid transparent;
        }

        .scenario-item:hover {
            transform: translateY(-5px);
            border-color: var(--accent-color);
        }

        .scenario-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            display: block;
        }

        /* 技术原理展示 */
        .tech-process {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 2rem 0;
            gap: 1rem;
        }

        .process-step {
            flex: 1;
            text-align: center;
            position: relative;
            background: #f8fafc;
            padding: 2rem 1rem;
            border-radius: var(--border-radius);
            border: 2px solid #e2e8f0;
        }

        .process-step::after {
            content: '→';
            position: absolute;
            right: -1.5rem;
            top: 50%;
            transform: translateY(-50%);
            font-size: 2rem;
            color: var(--accent-color);
            font-weight: bold;
            background: white;
            padding: 0 0.5rem;
        }

        .process-step:last-child::after {
            display: none;
        }

        .step-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            display: block;
        }

        /* 安全挑战展示 */
        .challenge-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 2rem;
            margin: 2rem 0;
        }

        .challenge-item {
            background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
            border: 2px solid var(--danger-color);
            border-radius: var(--border-radius);
            padding: 2rem;
            text-align: center;
        }

        .challenge-icon {
            font-size: 3rem;
            color: var(--danger-color);
            margin-bottom: 1rem;
            display: block;
        }

        /* 解决方案展示 */
        .solution-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 2rem;
            margin: 2rem 0;
        }

        .solution-item {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            border: 2px solid var(--success-color);
            border-radius: var(--border-radius);
            padding: 2rem;
            text-align: center;
        }

        .solution-icon {
            font-size: 3rem;
            color: var(--success-color);
            margin-bottom: 1rem;
            display: block;
        }

        /* 对比展示 */
        .comparison {
            display: flex;
            gap: 2rem;
            margin: 2rem 0;
        }

        .comparison-item {
            flex: 1;
            padding: 2rem;
            border-radius: var(--border-radius);
            text-align: center;
        }

        .before {
            background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
            border: 2px solid var(--danger-color);
        }

        .after {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            border: 2px solid var(--success-color);
        }

        /* 导航控件 */
        .nav-controls {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            display: flex;
            gap: 1rem;
            z-index: 1000;
        }

        .nav-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 1rem 1.5rem;
            border-radius: 50px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(37,99,235,0.3);
        }

        .nav-btn:hover {
            background: var(--secondary-color);
            transform: translateY(-2px);
        }

        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* 进度条 */
        .progress-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            height: 4px;
            background: var(--tech-gradient);
            transition: width 0.3s ease;
            z-index: 1000;
        }

        /* 页面指示器 */
        .slide-indicator {
            position: fixed;
            bottom: 2rem;
            left: 2rem;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            z-index: 1000;
        }

        /* 强调框 */
        .highlight-box {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 2px solid var(--warning-color);
            border-radius: var(--border-radius);
            padding: 2rem;
            margin: 2rem 0;
            text-align: center;
            font-size: 1.2rem;
            font-weight: 500;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .scenario-grid { grid-template-columns: repeat(2, 1fr); }
            .challenge-grid, .solution-grid { grid-template-columns: 1fr; }
        }

        @media (max-height: 800px) {
            .slide h1 { font-size: 3rem; }
            .slide h2 { font-size: 2rem; }
            .slide h3 { font-size: 1.5rem; }
            .slide p { font-size: 1.1rem; }
            .hero-subtitle { font-size: 1.6rem; }
            .scenario-item, .process-step { padding: 1.5rem; }
        }
    </style>
</head>

<body>
    <div class="presentation-container">
        <!-- 第1页：封面 - 人脸识别改变生活 -->
        <div class="slide active">
            <div class="hero-section">
                <h1>🤖 人脸识别技术</h1>
                <p class="hero-subtitle">从便利生活到安全保障的技术之旅</p>
                <div style="background: rgba(255,255,255,0.2); padding: 2rem; border-radius: 15px; margin-top: 2rem;">
                    <p style="font-size: 1.5rem; margin: 0;">让我们一起探索这项改变我们日常生活的神奇技术</p>
                </div>
            </div>
        </div>

        <!-- 第2页：人脸识别带来的便利 -->
        <div class="slide">
            <h2>📱 人脸识别让生活更便利</h2>
            <p style="font-size: 1.4rem; text-align: center; margin-bottom: 2rem; color: var(--text-light);">
                你可能每天都在使用，却不知道背后的技术原理
            </p>

            <div class="scenario-grid">
                <div class="scenario-item">
                    <span class="scenario-icon">📱</span>
                    <h3>手机解锁</h3>
                    <p>看一眼就能解锁，比指纹更方便，比密码更快捷</p>
                </div>
                <div class="scenario-item">
                    <span class="scenario-icon">💳</span>
                    <h3>移动支付</h3>
                    <p>刷脸支付，无需掏手机，购物结账只需1秒</p>
                </div>
                <div class="scenario-item">
                    <span class="scenario-icon">🚪</span>
                    <h3>门禁考勤</h3>
                    <p>公司、小区、学校，刷脸进出，告别忘带卡的烦恼</p>
                </div>
                <div class="scenario-item">
                    <span class="scenario-icon">✈️</span>
                    <h3>机场安检</h3>
                    <p>快速通关，自动验证身份，旅行更加顺畅</p>
                </div>
                <div class="scenario-item">
                    <span class="scenario-icon">🏥</span>
                    <h3>医院挂号</h3>
                    <p>刷脸就医，减少排队时间，提升就医体验</p>
                </div>
                <div class="scenario-item">
                    <span class="scenario-icon">📷</span>
                    <h3>照片整理</h3>
                    <p>自动识别照片中的人物，智能分类管理相册</p>
                </div>
            </div>

            <div class="highlight-box">
                💡 <strong>你知道吗？</strong> 全球每天有超过10亿次人脸识别操作，这项技术已经深度融入我们的日常生活
            </div>
        </div>

        <!-- 第3页：人脸识别是如何工作的 -->
        <div class="slide">
            <h2>🔍 人脸识别是如何认出你的？</h2>
            <p style="font-size: 1.4rem; text-align: center; margin-bottom: 2rem; color: var(--text-light);">
                就像人类记住朋友的脸一样，计算机也有自己的"记忆"方式
            </p>

            <div class="tech-process">
                <div class="process-step">
                    <span class="step-icon">📷</span>
                    <h3>1. 拍照</h3>
                    <p>摄像头捕捉你的脸部图像，就像给你拍了一张照片</p>
                </div>
                <div class="process-step">
                    <span class="step-icon">🎯</span>
                    <h3>2. 找脸</h3>
                    <p>计算机在图像中找到人脸位置，忽略背景和其他物体</p>
                </div>
                <div class="process-step">
                    <span class="step-icon">📏</span>
                    <h3>3. 测量</h3>
                    <p>测量眼睛间距、鼻子长度等特征，形成独特的"脸部指纹"</p>
                </div>
                <div class="process-step">
                    <span class="step-icon">🔍</span>
                    <h3>4. 比对</h3>
                    <p>与数据库中的"脸部指纹"进行比较，找到匹配的身份</p>
                </div>
            </div>

            <div style="background: #f0f9ff; border: 2px solid var(--accent-color); border-radius: var(--border-radius); padding: 2rem; margin-top: 2rem;">
                <h3 style="color: var(--accent-color); text-align: center; margin-bottom: 1rem;">🧠 简单理解</h3>
                <p style="text-align: center; font-size: 1.3rem;">
                    就像你能认出朋友的脸一样，计算机通过记住每个人脸部的独特特征来识别身份。
                    <br><strong>不同的是，计算机的"记忆"更精确，能同时记住数百万张脸！</strong>
                </p>
            </div>
        </div>

        <!-- 第4页：安全挑战 - 坏人的"伪装术" -->
        <div class="slide">
            <h2>⚠️ 安全挑战：坏人的"伪装术"</h2>
            <p style="font-size: 1.4rem; text-align: center; margin-bottom: 2rem; color: var(--text-light);">
                技术越先进，破解的方法也越来越狡猾
            </p>

            <div class="challenge-grid">
                <div class="challenge-item">
                    <span class="challenge-icon">📸</span>
                    <h3>照片攻击</h3>
                    <p>用你的照片对着摄像头，试图冒充你的身份进行解锁或支付</p>
                </div>
                <div class="challenge-item">
                    <span class="challenge-icon">📱</span>
                    <h3>视频攻击</h3>
                    <p>播放你的视频录像，让系统误以为是真人在操作</p>
                </div>
                <div class="challenge-item">
                    <span class="challenge-icon">🎭</span>
                    <h3>面具攻击</h3>
                    <p>制作高仿真面具或使用硅胶人脸，物理伪装成他人</p>
                </div>
                <div class="challenge-item">
                    <span class="challenge-icon">🤖</span>
                    <h3>AI换脸</h3>
                    <p>使用深度伪造技术，实时改变视频中的人脸</p>
                </div>
            </div>

            <div class="highlight-box" style="background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%); border-color: var(--danger-color);">
                🚨 <strong>真实案例：</strong> 2019年，有人用高清照片成功骗过了某品牌手机的人脸识别，盗刷了用户的移动支付账户
            </div>

            <div style="background: #f8fafc; border: 2px solid #e2e8f0; border-radius: var(--border-radius); padding: 2rem; margin-top: 2rem;">
                <h3 style="text-align: center; margin-bottom: 1rem;">🤔 问题来了</h3>
                <p style="text-align: center; font-size: 1.3rem;">
                    如何让系统知道面前的是<strong>真人</strong>，而不是照片、视频或面具？
                    <br><span style="color: var(--accent-color); font-weight: bold;">这就需要"活体检测"技术！</span>
                </p>
            </div>
        </div>

        <!-- 第5页：活体检测技术 - 真人验证 -->
        <div class="slide">
            <h2>🔐 活体检测：确保面前是真人</h2>
            <p style="font-size: 1.4rem; text-align: center; margin-bottom: 2rem; color: var(--text-light);">
                就像医生检查你是否还活着一样，系统也要确认你是真人
            </p>

            <div class="solution-grid">
                <div class="solution-item">
                    <span class="solution-icon">👁️</span>
                    <h3>眨眼检测</h3>
                    <p>要求你眨眨眼，照片和视频做不到自然的眨眼动作</p>
                </div>
                <div class="solution-item">
                    <span class="solution-icon">😮</span>
                    <h3>张嘴检测</h3>
                    <p>让你张开嘴巴，检测口腔内部，平面图像无法模拟</p>
                </div>
                <div class="solution-item">
                    <span class="solution-icon">🌡️</span>
                    <h3>体温检测</h3>
                    <p>感应人体温度，面具和照片没有真人的体温特征</p>
                </div>
                <div class="solution-item">
                    <span class="solution-icon">📐</span>
                    <h3>3D检测</h3>
                    <p>测量脸部立体结构，区分真实人脸和平面图像</p>
                </div>
            </div>

            <div style="background: #f0f9ff; border: 2px solid var(--accent-color); border-radius: var(--border-radius); padding: 2rem; margin: 2rem 0;">
                <h3 style="color: var(--accent-color); text-align: center; margin-bottom: 1rem;">💡 聪明的组合</h3>
                <p style="text-align: center; font-size: 1.3rem;">
                    现代活体检测会同时使用多种方法，就像多道安全门一样，
                    <br><strong>让坏人更难蒙混过关！</strong>
                </p>
            </div>

            <div class="comparison">
                <div class="comparison-item before">
                    <h3 style="color: var(--danger-color);">❌ 没有活体检测</h3>
                    <p>• 照片可以骗过系统</p>
                    <p>• 视频可以冒充真人</p>
                    <p>• 面具可能成功伪装</p>
                    <p>• 安全风险很高</p>
                </div>
                <div class="comparison-item after">
                    <h3 style="color: var(--success-color);">✅ 有了活体检测</h3>
                    <p>• 必须是真人才能通过</p>
                    <p>• 多重验证更安全</p>
                    <p>• 伪造攻击被拦截</p>
                    <p>• 安全性大大提升</p>
                </div>
            </div>
        </div>

        <!-- 第6页：总结 - 技术让生活更美好 -->
        <div class="slide">
            <h2>🌟 技术让生活更美好</h2>

            <div style="text-align: center; margin: 2rem 0;">
                <div style="background: var(--tech-gradient); color: white; padding: 3rem; border-radius: 20px; margin-bottom: 2rem;">
                    <h3 style="color: white; margin-bottom: 1rem;">🚀 从便利到安全的技术进化</h3>
                    <p style="font-size: 1.4rem; margin: 0;">
                        人脸识别技术不断进步，在带来便利的同时，也在不断提升安全性
                    </p>
                </div>
            </div>

            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 2rem; margin: 2rem 0;">
                <div style="background: #f0f9ff; border: 2px solid var(--accent-color); border-radius: var(--border-radius); padding: 2rem; text-align: center;">
                    <span style="font-size: 3rem; display: block; margin-bottom: 1rem;">⚡</span>
                    <h3>更快速</h3>
                    <p>毫秒级识别，比传统方式快100倍</p>
                </div>
                <div style="background: #f0fdf4; border: 2px solid var(--success-color); border-radius: var(--border-radius); padding: 2rem; text-align: center;">
                    <span style="font-size: 3rem; display: block; margin-bottom: 1rem;">🛡️</span>
                    <h3>更安全</h3>
                    <p>活体检测让伪造攻击无处遁形</p>
                </div>
                <div style="background: #fefce8; border: 2px solid var(--warning-color); border-radius: var(--border-radius); padding: 2rem; text-align: center;">
                    <span style="font-size: 3rem; display: block; margin-bottom: 1rem;">🌍</span>
                    <h3>更普及</h3>
                    <p>从手机到城市，无处不在的智能识别</p>
                </div>
            </div>

            <div class="highlight-box">
                🎯 <strong>未来展望：</strong> 随着技术不断发展，人脸识别将变得更加智能、安全和人性化，
                为我们创造一个既便利又安全的数字世界
            </div>
        </div>
    </div>

    <!-- 导航控件 -->
    <div class="nav-controls">
        <button class="nav-btn" id="prevBtn" onclick="changeSlide(-1)">← 上一页</button>
        <button class="nav-btn" id="nextBtn" onclick="changeSlide(1)">下一页 →</button>
    </div>

    <!-- 进度条 -->
    <div class="progress-bar" id="progressBar"></div>

    <!-- 页面指示器 -->
    <div class="slide-indicator" id="slideIndicator">1 / 6</div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;

        function showSlide(n) {
            slides[currentSlide].classList.remove('active');
            currentSlide = (n + totalSlides) % totalSlides;
            slides[currentSlide].classList.add('active');

            // 更新进度条
            const progress = ((currentSlide + 1) / totalSlides) * 100;
            document.getElementById('progressBar').style.width = progress + '%';

            // 更新页面指示器
            document.getElementById('slideIndicator').textContent = `${currentSlide + 1} / ${totalSlides}`;

            // 更新导航按钮
            document.getElementById('prevBtn').disabled = currentSlide === 0;
            document.getElementById('nextBtn').disabled = currentSlide === totalSlides - 1;
        }

        function changeSlide(direction) {
            showSlide(currentSlide + direction);
        }

        // 键盘导航
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft' || e.key === 'ArrowUp') {
                changeSlide(-1);
            } else if (e.key === 'ArrowRight' || e.key === 'ArrowDown' || e.key === ' ') {
                changeSlide(1);
            }
        });

        // 初始化
        showSlide(0);

        // 添加平滑过渡
        slides.forEach(slide => {
            slide.style.transition = 'opacity 0.5s ease-in-out';
        });
    </script>
</body>
</html>
